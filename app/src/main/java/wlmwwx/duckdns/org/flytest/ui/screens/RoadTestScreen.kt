package wlmwwx.duckdns.org.flytest.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import wlmwwx.duckdns.org.flytest.ui.viewmodels.RoadTestViewModel
import java.util.Locale
import wlmwwx.duckdns.org.flytest.utils.FormatUtils.format
import wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel
import wlmwwx.duckdns.org.flytest.logic.model.LocationInfoModel
import android.widget.Toast
import androidx.activity.compose.BackHandler
import android.util.Log

// AMap Imports
import com.amap.api.maps.AMap
import com.amap.api.maps.CameraUpdateFactory
import com.amap.api.maps.MapView
import com.amap.api.maps.MapsInitializer
import com.amap.api.maps.model.LatLng
import com.amap.api.maps.model.Marker
import com.amap.api.maps.model.MarkerOptions
import com.amap.api.maps.model.Polyline
import com.amap.api.maps.model.PolylineOptions
// import com.amap.api.maps.model.BitmapDescriptorFactory // Optional for custom marker
import android.graphics.Color as AndroidColor // Aliasing to avoid conflict with Compose Color

import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.compose.ui.platform.LocalLifecycleOwner
import android.os.Bundle

@Composable
fun RoadTestScreen(
    paddingValues: PaddingValues,
    // viewModel: RoadTestViewModel = viewModel(),
    onNavigateToSettings: () -> Unit = {},
    onNavigateToHistory: () -> Unit = {},
    onBackPressed: () -> Unit = {},
) {
    val viewModel = viewModel<RoadTestViewModel>()
    val isRecording by viewModel.isRecording.collectAsState()
    val context = LocalContext.current
    var showExitDialog by remember { mutableStateOf(false) }
    val locationInfo = viewModel.locationInfo.collectAsState().value // Collect once
    val trajectoryPoints by viewModel.trajectoryPoints.collectAsState()

    // var aMap by remember { mutableStateOf<AMap?>(null) } // Moved to MapInfoPanel
    // var currentLocationMarker by remember { mutableStateOf<Marker?>(null) } // Moved to MapInfoPanel
    // var trajectoryPolyline by remember { mutableStateOf<Polyline?>(null) } // Moved to MapInfoPanel

    // Initialize AMap SDK - This LaunchedEffect will be removed as initialization is moved to rememberMapViewWithLifecycle
     LaunchedEffect(Unit) {
         try {
             MapsInitializer.updatePrivacyAgree(context,  true)
             MapsInitializer.updatePrivacyShow(context,true,true)
             MapsInitializer.initialize(context.applicationContext)
         } catch (e: Exception) {
             Log.e("AMapInit", "Error initializing AMap SDK in RoadTestScreen", e)
             Toast.makeText(context, "地图服务初始化失败", Toast.LENGTH_LONG).show()
         }
     }

    // Map related LaunchedEffects are moved into MapInfoPanel

    LaunchedEffect(isRecording) {
        if (isRecording) {
            Toast.makeText(context, "Recording started", Toast.LENGTH_SHORT).show()
        }
    }

    BackHandler(enabled = true) {
        if (isRecording) {
            showExitDialog = true
        } else {
            onBackPressed()
        }
    }

    if (showExitDialog) {
        AlertDialog(
            onDismissRequest = { showExitDialog = false },
            title = { Text("正在进行测试") },
            text = { Text("返回将停止测试，是否确认？") },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.toggleRecording()  // 停止录制
                        showExitDialog = false
                        onBackPressed()
                    }
                ) {
                    Text("确认")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showExitDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }

    Scaffold(
        bottomBar = { BottomControls(isRecording = isRecording, onToggleRecording = { viewModel.toggleRecording() }, onSettingsClick = onNavigateToSettings, onHistoryClick = onNavigateToHistory) }
    ) { innerPadding ->
        val scrollState = rememberScrollState()
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)  // 处理来自 MainScreen 的 toolbar padding
                .padding(innerPadding)   // 处理 bottomBar 的 padding
                .padding(horizontal = 8.dp)
                .verticalScroll(scrollState)  // 添加滚动功能
        ) {
            // val locationInfo = viewModel.locationInfo.collectAsState().value // Already collected above
            val lteInfo = viewModel.lteInfo.collectAsState().value
            val nrInfo = viewModel.nrInfo.collectAsState().value
            val wcdmaInfo = viewModel.wcdmaInfo.collectAsState().value
            val gsmInfo = viewModel.gsmInfo.collectAsState().value
            val uploadSpeed = viewModel.uploadSpeed.collectAsState().value
            val downloadSpeed = viewModel.downloadSpeed.collectAsState().value

            LocationInfoPanel(locationInfo = locationInfo)
            Spacer(modifier = Modifier.height(16.dp)) // Spacer after LocationInfoPanel

            // Network Info Panels
            val cellType = viewModel.cellType.collectAsState().value
            when (cellType) {
                "LTE" -> LteInfoPanel(
                    lteInfo = lteInfo,
                    uploadSpeed = uploadSpeed,
                    downloadSpeed = downloadSpeed
                )
                "NR" -> NrInfoPanel(
                    nrInfo = nrInfo,
                    uploadSpeed = uploadSpeed,
                    downloadSpeed = downloadSpeed
                )
                "WCDMA" -> WcdmaInfoPanel(
                    wcdmaInfo = wcdmaInfo
                )
                "GSM" -> GsmInfoPanel(
                    gsmInfo = gsmInfo
                )
                else -> LteInfoPanel( // Default panel, can be an empty state or LTE
                    lteInfo = lteInfo,
                    uploadSpeed = uploadSpeed,
                    downloadSpeed = downloadSpeed
                )
            }

            Spacer(modifier = Modifier.height(16.dp)) // Spacer before MapInfoPanel

            MapInfoPanel(
                locationInfo = locationInfo,
                trajectoryPoints = trajectoryPoints,
                isRecording = isRecording,
                modifier = Modifier.fillMaxWidth() // Apply fillMaxWidth to the panel itself
            )
            // Add a final spacer if needed for bottom padding in scrollable content
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}


@Composable
fun MapInfoPanel(
    locationInfo: LocationInfoModel.LocationInfo,
    trajectoryPoints: List<LocationInfoModel.LocationInfo>,
    isRecording: Boolean,
    modifier: Modifier = Modifier // Modifier for the panel container (e.g. Card)
) {
    var aMap by remember { mutableStateOf<AMap?>(null) }
    var currentLocationMarker by remember { mutableStateOf<Marker?>(null) }
    var trajectoryPolyline by remember { mutableStateOf<Polyline?>(null) }
    val context = LocalContext.current // Needed for Toast if AMap init fails within rememberMapViewWithLifecycle

    // Initial Map Camera Position
    LaunchedEffect(aMap, locationInfo) {
        aMap?.let { map ->
            // Move camera only if it's at the default position (0,0) and a valid location is available
            // if (locationInfo.isValid && map.cameraPosition.target.latitude == 0.0 && map.cameraPosition.target.longitude == 0.0) {
            if (locationInfo.isValid ) {
                map.moveCamera(CameraUpdateFactory.newLatLngZoom(LatLng(locationInfo.latitude, locationInfo.longitude), 15f))
            }
        }
    }

    // Camera behavior on recording start
    LaunchedEffect(isRecording, aMap, locationInfo) {
        if (isRecording && aMap != null && locationInfo.isValid) {
            aMap?.animateCamera(CameraUpdateFactory.newLatLngZoom(LatLng(locationInfo.latitude, locationInfo.longitude), 16f), 1000, null)
        }
    }

    // Update Current Location Marker
    LaunchedEffect(aMap, locationInfo) {
        aMap?.let { map ->
            if (locationInfo.isValid) {
                val currentLatLng = LatLng(locationInfo.latitude, locationInfo.longitude)
                if (currentLocationMarker == null) {
                    currentLocationMarker = map.addMarker(
                        MarkerOptions().position(currentLatLng).title("Current Location")
                    )
                } else {
                    currentLocationMarker?.position = currentLatLng
                }
            }
            // Consider removing marker if location becomes invalid:
            // else {
            //     currentLocationMarker?.remove()
            //     currentLocationMarker = null
            // }
        }
    }

    // Update Trajectory Polyline
    LaunchedEffect(aMap, trajectoryPoints) {
        aMap?.let { map ->
            if (trajectoryPoints.size > 1) {
                val latLngList = trajectoryPoints.map { LatLng(it.latitude, it.longitude) }
                if (trajectoryPolyline == null) {
                    trajectoryPolyline = map.addPolyline(
                        PolylineOptions()
                            .addAll(latLngList)
                            .color(AndroidColor.BLUE)
                            .width(10f)
                    )
                } else {
                    trajectoryPolyline?.points = latLngList
                }
            } else {
                trajectoryPolyline?.remove()
                trajectoryPolyline = null
            }
        }
    }

    // The AMapView itself, which will be part of the MapInfoPanel
    // The modifier passed to MapInfoPanel can be used here if MapInfoPanel is a direct wrapper
    // or applied to a Card if MapInfoPanel becomes a Card.
    // For now, MapInfoPanel directly contains AMapView.
    AMapView(
        modifier = modifier // Use the modifier passed to MapInfoPanel for the AMapView
            .height(300.dp) // Specific height for the map content
            .clip(RoundedCornerShape(8.dp)),
        onMapReady = { map ->
            aMap = map
            map.uiSettings.isZoomControlsEnabled = false
        }
    )
}

//region AMapView Composables
@Composable
fun rememberMapViewWithLifecycle(): MapView {
    val context = LocalContext.current
    // AMap SDK initialization is now handled here
    LaunchedEffect(Unit) {
        try {
            MapsInitializer.updatePrivacyAgree(context,  true)
            MapsInitializer.updatePrivacyShow(context, true, true)
            MapsInitializer.initialize(context.applicationContext)
        } catch (e: Exception) {
            Log.e("AMapInit", "Error initializing AMap SDK in rememberMapViewWithLifecycle", e)
            Toast.makeText(context, "地图服务初始化失败", Toast.LENGTH_LONG).show()
        }
    }

    val mapView = remember {
        MapView(context)
        // Application of `onCreate` is handled by the lifecycle observer.
    }

    val lifecycleObserver = rememberMapLifecycleObserver(mapView)
    val lifecycle = LocalLifecycleOwner.current.lifecycle
    DisposableEffect(lifecycle, mapView) {
        lifecycle.addObserver(lifecycleObserver)
        onDispose {
            lifecycle.removeObserver(lifecycleObserver)
        }
    }
    return mapView
}

@Composable
private fun rememberMapLifecycleObserver(mapView: MapView): LifecycleEventObserver =
    remember(mapView) {
        LifecycleEventObserver { _, event ->
            val bundle = Bundle() // Default bundle for onSaveInstanceState
            when (event) {
                Lifecycle.Event.ON_CREATE -> mapView.onCreate(null)
                Lifecycle.Event.ON_RESUME -> mapView.onResume()
                Lifecycle.Event.ON_PAUSE -> mapView.onPause()
                Lifecycle.Event.ON_STOP -> mapView.onSaveInstanceState(bundle)
                Lifecycle.Event.ON_DESTROY -> mapView.onDestroy()
                else -> { /* Other events can be handled here if needed */ }
            }
        }
    }

@Composable
fun AMapView(
    modifier: Modifier = Modifier,
    onMapReady: (AMap) -> Unit
) {
    val mapView = rememberMapViewWithLifecycle()

    AndroidView(
        factory = { mapView },
        modifier = modifier
    ) { view ->
        view.map?.let {
            onMapReady(it)
        }
    }
}
//endregion

@Composable
fun LteInfoPanel(
    lteInfo: wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel.LteInfo,
    uploadSpeed: String,
    downloadSpeed: String
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("基站信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text("当前网络: LTE", style = MaterialTheme.typography.bodyMedium, color = Color.Gray)
                    Spacer(modifier = Modifier.width(8.dp))
                    OutlinedButton(
                        onClick = { /* TODO: Handle neighbors info click */ },
                        contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp),
                        modifier = Modifier.height(30.dp)
                    ) {
                        Text("邻区信息", fontSize = 12.sp)
                    }
                }
            }
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("ENB:", lteInfo.eci.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CELLID:", lteInfo.ci.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("频点:", lteInfo.earfcn.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("PCI:", lteInfo.pci.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("TAC:", lteInfo.tac.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("RSRP:", lteInfo.rsrp.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("RSRQ:", lteInfo.rsrq.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("SINR:", lteInfo.sinr.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("RSSI:", lteInfo.rssi.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("UL:", uploadSpeed)
                InfoItem("DL:", downloadSpeed)
            }

            // Spacer(modifier = Modifier.height(12.dp))
            // SignalTypeToggle() // RSRP/SINR Toggle
        }
    }
}

@Composable
fun NrInfoPanel(
    nrInfo: wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel.NrInfo,
    uploadSpeed: String,
    downloadSpeed: String,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Text("5G NR 信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("MCC:", nrInfo.mcc)
                InfoItem("MNC:", nrInfo.mnc)
                InfoItem("NCI:", nrInfo.nci.takeIf { it != Long.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("TAC:", nrInfo.tac.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("PCI:", nrInfo.pci.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("NRARFCN:", nrInfo.nrarfcn.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("CSI RSRP:", nrInfo.csi_rsrp.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CSI RSRQ:", nrInfo.csi_rsrq.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CSI SINR:", nrInfo.csi_sinr.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("SS RSRP:", nrInfo.ss_rsrp.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("SS RSRQ:", nrInfo.ss_rsrq.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("SS SINR:", nrInfo.ss_sinr.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("UL:", uploadSpeed)
                InfoItem("DL:", downloadSpeed)
            }
        }
    }
}

@Composable
fun WcdmaInfoPanel(
    wcdmaInfo: wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel.WcdmaInfo,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Text("WCDMA 信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("MCC:", wcdmaInfo.mcc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("MNC:", wcdmaInfo.mnc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CID:", wcdmaInfo.cid.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("LAC:", wcdmaInfo.lac.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("PSC:", wcdmaInfo.psc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("UARFCN:", wcdmaInfo.uarfcn.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("RSSI:", wcdmaInfo.rssi.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("RxLev:", wcdmaInfo.rxlev.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
        }
    }
}

@Composable
fun GsmInfoPanel(
    gsmInfo: wlmwwx.duckdns.org.flytest.logic.model.RadioInfoModel.GsmInfo,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Text("GSM 信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("MCC:", gsmInfo.mcc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("MNC:", gsmInfo.mnc.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("CID:", gsmInfo.cid.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("LAC:", gsmInfo.lac.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("ARFCN:", gsmInfo.arfcn.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("BSIC:", gsmInfo.bsic.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("RSSI:", gsmInfo.rssi.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
                InfoItem("RxLev:", gsmInfo.rxlev.takeIf { it != Int.MAX_VALUE }?.toString() ?: "N/A")
            }
        }
    }
}

@Composable
fun LocationInfoPanel(locationInfo: wlmwwx.duckdns.org.flytest.logic.model.LocationInfoModel.LocationInfo) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(modifier = Modifier.padding(12.dp)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text("定位信息", style = MaterialTheme.typography.titleMedium, fontWeight = FontWeight.Bold)
                Text(
                    text = if (locationInfo.isValid) "有效" else "无效",
                    style = MaterialTheme.typography.bodySmall,
                    color = if (locationInfo.isValid) Color.Green else Color.Red
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Divider()
            Spacer(modifier = Modifier.height(12.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("经度", String.format(Locale.US, "%.6f", locationInfo.longitude))
                InfoItem("纬度", String.format(Locale.US, "%.6f", locationInfo.latitude))
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("海拔", String.format(Locale.US, "%.1f m", locationInfo.altitude))
                InfoItem("速度", String.format(Locale.US, "%.1f m/s", locationInfo.speed))
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
                InfoItem("精度", String.format(Locale.US, "%.1f m", locationInfo.accuracy))
                InfoItem("来源", locationInfo.provider)
            }
            if (locationInfo.bearing != 0f) {
                Spacer(modifier = Modifier.height(4.dp))
                Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Start) {
                    InfoItem("方向", String.format(Locale.US, "%.1f°", locationInfo.bearing))
                }
            }
        }
    }
}

@Composable
fun InfoItem(label: String, value: String, modifier: Modifier = Modifier) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = Color.Gray,
            modifier = Modifier.width(50.dp) // Adjust width as needed for alignment
        )
        Text(text = value, style = MaterialTheme.typography.bodyMedium, fontWeight = FontWeight.Medium)
    }
}

@Composable
fun SignalTypeToggle() {
    var selectedOption by remember { mutableStateOf("RSRP") }
    Row(verticalAlignment = Alignment.CenterVertically) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            RadioButton(
                selected = selectedOption == "RSRP",
                onClick = { selectedOption = "RSRP" },
                colors = RadioButtonDefaults.colors(selectedColor = MaterialTheme.colorScheme.primary)
            )
            Text("RSRP", style = MaterialTheme.typography.bodyMedium)
        }
        Spacer(modifier = Modifier.width(16.dp))
        Row(verticalAlignment = Alignment.CenterVertically) {
            RadioButton(
                selected = selectedOption == "SINR",
                onClick = { selectedOption = "SINR" },
                colors = RadioButtonDefaults.colors(selectedColor = MaterialTheme.colorScheme.primary)
            )
            Text("SINR", style = MaterialTheme.typography.bodyMedium)
        }
         Spacer(modifier = Modifier.width(16.dp))
         Text("图例", style = MaterialTheme.typography.bodyMedium, color = Color.Gray) // Legend
         // TODO: Add Arrow Up icon if needed
    }
}


@Composable
fun StatItem(label: String, value: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyLarge,
            color = Color.Gray
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun BottomControls(
    isRecording: Boolean,
    onToggleRecording: () -> Unit,
    onSettingsClick: () -> Unit,
    onHistoryClick: () -> Unit
) {
    BottomAppBar(
        containerColor = MaterialTheme.colorScheme.primaryContainer
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = onSettingsClick,
                enabled = !isRecording
            ) {
                Icon(
                    Icons.Filled.Settings,
                    contentDescription = "Settings",
                    tint = if (isRecording) MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.38f)
                          else MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            IconButton(onClick = onToggleRecording) {
                Icon(
                    imageVector = if (isRecording) Icons.Filled.Stop else Icons.Filled.PlayArrow,
                    contentDescription = if (isRecording) "Stop Recording" else "Start Recording",
                    tint = if (isRecording) Color.Red else MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
            IconButton(
                onClick = onHistoryClick,
                enabled = !isRecording
            ) {
                Icon(
                    Icons.Filled.Refresh,
                    contentDescription = "History",
                    tint = if (isRecording) MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.38f)
                          else MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
    }
}
